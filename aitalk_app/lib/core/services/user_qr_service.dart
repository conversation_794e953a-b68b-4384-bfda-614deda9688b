import '../models/user_qr_data.dart';
import '../user/user_profile.dart';
import '../device/device_manager.dart';
import '../protocol/at_commands.dart';
import '../protocol/at_response_parser.dart';
import '../protocol/at_response_listener.dart';
import '../bluetooth/bluetooth_manager.dart';
import '../bluetooth/passthrough_gatt_helper.dart';
import '../constants/rate_mode_payload.dart';
import 'dart:async';
import 'package:flutter/foundation.dart';

/// 用户二维码服务
/// 负责生成包含用户频点和速率信息的二维码数据
class UserQrService {
  UserQrService._();

  /// 获取当前用户的二维码数据
  ///
  /// 返回包含以下信息的二维码数据：
  /// - 当前频点（通过AT+FREQ?查询）
  /// - 当前速率模式（通过AT+RATE?查询）
  /// - 用户昵称
  /// - 设备ID
  static Future<UserQrData?> getCurrentUserQrData() async {
    try {
      // 获取用户昵称
      final nickname = UserProfile.instance.nicknameNotifier.value ?? '未知用户';

      // 获取设备ID
      final deviceId = DeviceManager.instance.deviceIdNotifier.value ?? '未知设备';

      // 查询当前频点和速率
      // 优先尝试通过AT指令查询，如果失败则使用fallback值
      final frequency =
          await _queryCurrentFrequency() ?? _getFallbackFrequency();
      final rateMode = await _queryCurrentRate() ?? _getFallbackRateMode();

      return UserQrData(
        frequency: frequency,
        rateMode: rateMode,
        nickname: nickname,
        deviceId: deviceId,
      );
    } catch (e) {
      debugPrint('❌ 获取二维码数据失败: $e');
      return null;
    }
  }

  /// 查询当前频点
  /// 发送AT+FREQ?指令并解析响应
  static Future<int?> _queryCurrentFrequency() async {
    try {
      final device = BluetoothManager.currentDevice.value;
      if (device == null) {
        debugPrint('❌ 未连接设备，无法查询频点');
        return null;
      }

      // 确保响应监听器已初始化
      await AtResponseListener.initialize();

      // 发送AT+FREQ?查询指令
      final cmdBytes = getAtCommandBytes(AtCommandType.queryFreq);
      await PassthroughGattHelper.sendAtCommand(
        device,
        cmdBytes,
        withoutResponse: true,
      );

      // 等待响应
      final response = await AtResponseListener.waitForResponse('FREQ');
      if (response != null) {
        // 解析响应获取频点值
        final frequency = AtResponseParser.parseFrequencyResponse(response);
        if (frequency != null) {
          debugPrint('✅ 查询到当前频点: $frequency Hz');
          return frequency;
        }
      }

      debugPrint('⚠️ 未收到有效的频点响应');
      return null;
    } catch (e) {
      debugPrint('❌ 查询频点失败: $e');
      return null;
    }
  }

  /// 查询当前速率模式
  /// 发送AT+RATE?指令并解析响应
  static Future<int?> _queryCurrentRate() async {
    try {
      final device = BluetoothManager.currentDevice.value;
      if (device == null) {
        debugPrint('❌ 未连接设备，无法查询速率');
        return null;
      }

      // 确保响应监听器已初始化
      await AtResponseListener.initialize();

      // 发送AT+RATE?查询指令
      final cmdBytes = getAtCommandBytes(AtCommandType.queryRate);
      await PassthroughGattHelper.sendAtCommand(
        device,
        cmdBytes,
        withoutResponse: true,
      );

      // 等待响应
      final response = await AtResponseListener.waitForResponse('RATE');
      if (response != null) {
        // 解析响应获取速率值
        final rateMode = AtResponseParser.parseRateResponse(response);
        if (rateMode != null) {
          debugPrint('✅ 查询到当前速率模式: $rateMode');
          return rateMode;
        }
      }

      debugPrint('⚠️ 未收到有效的速率响应');
      return null;
    } catch (e) {
      debugPrint('❌ 查询速率失败: $e');
      return null;
    }
  }

  /// 生成二维码字符串
  static Future<String?> generateQrString() async {
    final qrData = await getCurrentUserQrData();
    return qrData?.toQrString();
  }

  /// 获取fallback频点值
  /// 当AT指令查询失败时使用默认值
  static int _getFallbackFrequency() {
    // 使用默认频点 483.6MHz (483600000 Hz)
    return 483600000;
  }

  /// 获取fallback速率模式值
  /// 当AT指令查询失败时使用默认值
  static int _getFallbackRateMode() {
    // 使用当前全局速率模式，如果没有则使用默认值6
    return RateModePayload.currentRateMode;
  }
}
