import '../models/user_qr_data.dart';
import '../user/user_profile.dart';
import '../device/device_manager.dart';
import '../constants/frequencies.dart';
import '../constants/rate_mode_payload.dart';
import 'channel_manager.dart';

/// 用户二维码服务
/// 负责生成包含用户频点和速率信息的二维码数据
class UserQrService {
  UserQrService._();

  /// 获取当前用户的二维码数据
  /// 
  /// 返回包含以下信息的二维码数据：
  /// - 当前频点
  /// - 当前速率模式
  /// - 用户昵称
  /// - 设备ID
  /// - 是否为公共频点
  /// - 信道号
  static Future<UserQrData?> getCurrentUserQrData() async {
    try {
      // 获取用户昵称
      final nickname = UserProfile.instance.nicknameNotifier.value ?? '未知用户';
      
      // 获取设备ID
      final deviceId = DeviceManager.instance.deviceIdNotifier.value ?? '未知设备';
      
      // 获取当前信道
      final currentChannel = ChannelManager.currentChannel.value;
      
      // 获取当前速率模式
      final currentRateMode = RateModePayload.currentRateMode;
      
      // 判断是否为公共频点（这里需要根据实际业务逻辑判断）
      // 暂时假设默认为公共频点，实际应用中可能需要根据当前聊天状态判断
      final isPublicChannel = await _determineChannelType();
      
      // 根据信道类型获取频点
      final frequency = isPublicChannel 
          ? Frequencies.publicChannelFreq(currentChannel)
          : Frequencies.privateChannelFreq(currentChannel);

      return UserQrData(
        frequency: frequency,
        rateMode: currentRateMode,
        nickname: nickname,
        deviceId: deviceId,
        isPublicChannel: isPublicChannel,
        channel: currentChannel,
      );
    } catch (e) {
      // 如果获取数据失败，返回null
      return null;
    }
  }

  /// 判断当前是否为公共频点
  /// 
  /// 这里的逻辑可能需要根据实际业务需求调整
  /// 比如根据当前活跃的聊天群组类型来判断
  static Future<bool> _determineChannelType() async {
    // TODO: 根据实际业务逻辑判断
    // 可能需要检查当前活跃的群组类型
    // 或者检查最近的频点切换记录
    
    // 暂时返回true（公共频点）
    // 实际实现时可能需要：
    // 1. 检查ActiveGroupStorage中的群组信息
    // 2. 或者在ChannelManager中添加频点类型记录
    // 3. 或者根据当前页面状态判断
    return true;
  }

  /// 生成二维码字符串
  static Future<String?> generateQrString() async {
    final qrData = await getCurrentUserQrData();
    return qrData?.toQrString();
  }
}
