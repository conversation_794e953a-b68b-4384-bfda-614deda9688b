import 'dart:convert';

/// 用户二维码数据模型
/// 包含用户的频点和速率信息
class UserQrData {
  /// 当前用户所在频点 (Hz)
  final int frequency;
  
  /// 当前用户使用的速率模式
  final int rateMode;
  
  /// 用户昵称
  final String nickname;
  
  /// 设备ID
  final String deviceId;
  
  /// 是否为公共频点
  final bool isPublicChannel;
  
  /// 信道号 (1-16)
  final int channel;

  const UserQrData({
    required this.frequency,
    required this.rateMode,
    required this.nickname,
    required this.deviceId,
    required this.isPublicChannel,
    required this.channel,
  });

  /// 转换为JSON字符串，用于生成二维码
  String toQrString() {
    final data = {
      'freq': frequency,
      'rate': rateMode,
      'name': nickname,
      'device': deviceId,
      'public': isPublicChannel,
      'channel': channel,
      'version': 1, // 协议版本号，便于后续扩展
    };
    return jsonEncode(data);
  }

  /// 从JSON字符串创建实例
  static UserQrData? fromQrString(String qrString) {
    try {
      final data = jsonDecode(qrString) as Map<String, dynamic>;
      return UserQrData(
        frequency: data['freq'] as int,
        rateMode: data['rate'] as int,
        nickname: data['name'] as String,
        deviceId: data['device'] as String,
        isPublicChannel: data['public'] as bool,
        channel: data['channel'] as int,
      );
    } catch (e) {
      return null;
    }
  }

  /// 获取频点显示文本 (MHz)
  String get frequencyDisplayText {
    return '${(frequency / 1000000).toStringAsFixed(2)} MHz';
  }

  /// 获取速率模式显示文本
  String get rateModeDisplayText {
    return '速率模式 $rateMode';
  }

  /// 获取信道类型显示文本
  String get channelTypeDisplayText {
    return isPublicChannel ? '公共信道' : '私有信道';
  }

  @override
  String toString() {
    return 'UserQrData(frequency: $frequency, rateMode: $rateMode, nickname: $nickname, deviceId: $deviceId, isPublicChannel: $isPublicChannel, channel: $channel)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserQrData &&
        other.frequency == frequency &&
        other.rateMode == rateMode &&
        other.nickname == nickname &&
        other.deviceId == deviceId &&
        other.isPublicChannel == isPublicChannel &&
        other.channel == channel;
  }

  @override
  int get hashCode {
    return Object.hash(
      frequency,
      rateMode,
      nickname,
      deviceId,
      isPublicChannel,
      channel,
    );
  }
}
