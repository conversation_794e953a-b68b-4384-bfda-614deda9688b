import 'dart:convert';

/// 用户二维码数据模型
/// 包含用户的频点和速率信息
class UserQrData {
  /// 当前用户所在频点 (Hz)
  final int frequency;

  /// 当前用户使用的速率模式
  final int rateMode;

  /// 用户昵称
  final String nickname;

  /// 设备ID
  final String deviceId;

  const UserQrData({
    required this.frequency,
    required this.rateMode,
    required this.nickname,
    required this.deviceId,
  });

  /// 转换为JSON字符串，用于生成二维码
  String toQrString() {
    final data = {
      'freq': frequency,
      'rate': rateMode,
      'name': nickname,
      'device': deviceId,
      'version': 1, // 协议版本号，便于后续扩展
    };
    return jsonEncode(data);
  }

  /// 从JSON字符串创建实例
  static UserQrData? fromQrString(String qrString) {
    try {
      final data = jsonDecode(qrString) as Map<String, dynamic>;
      return UserQrData(
        frequency: data['freq'] as int,
        rateMode: data['rate'] as int,
        nickname: data['name'] as String,
        deviceId: data['device'] as String,
      );
    } catch (e) {
      return null;
    }
  }

  /// 获取频点显示文本 (MHz)
  String get frequencyDisplayText {
    return '${(frequency / 1000000).toStringAsFixed(2)} MHz';
  }

  /// 获取速率模式显示文本
  String get rateModeDisplayText {
    return '速率模式 $rateMode';
  }

  @override
  String toString() {
    return 'UserQrData(frequency: $frequency, rateMode: $rateMode, nickname: $nickname, deviceId: $deviceId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserQrData &&
        other.frequency == frequency &&
        other.rateMode == rateMode &&
        other.nickname == nickname &&
        other.deviceId == deviceId;
  }

  @override
  int get hashCode {
    return Object.hash(frequency, rateMode, nickname, deviceId);
  }
}
