import 'dart:async';
import 'package:flutter/foundation.dart';

/// AT指令响应解析器
/// 用于解析设备返回的AT指令响应
class AtResponseParser {
  AtResponseParser._();

  /// 解析频点查询响应
  /// 响应格式: +FREQ:473200000,473200000,473200000,473200000
  /// 返回第一个频点值
  static int? parseFrequencyResponse(String response) {
    try {
      // 查找+FREQ:开头的行
      final lines = response.split('\n');
      for (final line in lines) {
        final trimmedLine = line.trim();
        if (trimmedLine.startsWith('+FREQ:')) {
          // 提取+FREQ:后面的内容
          final content = trimmedLine.substring(6); // 去掉"+FREQ:"
          // 分割逗号，取第一个数字
          final parts = content.split(',');
          if (parts.isNotEmpty) {
            final frequencyStr = parts[0].trim();
            return int.tryParse(frequencyStr);
          }
        }
      }
      return null;
    } catch (e) {
      debugPrint('❌ 解析频点响应失败: $e');
      return null;
    }
  }

  /// 解析速率查询响应
  /// 响应格式: +RATE:18,其他参数...
  /// 返回第一个速率值
  static int? parseRateResponse(String response) {
    try {
      // 查找+RATE:开头的行
      final lines = response.split('\n');
      for (final line in lines) {
        final trimmedLine = line.trim();
        if (trimmedLine.startsWith('+RATE:')) {
          // 提取+RATE:后面的内容
          final content = trimmedLine.substring(6); // 去掉"+RATE:"
          // 分割逗号，取第一个数字
          final parts = content.split(',');
          if (parts.isNotEmpty) {
            final rateStr = parts[0].trim();
            return int.tryParse(rateStr);
          }
        }
      }
      return null;
    } catch (e) {
      debugPrint('❌ 解析速率响应失败: $e');
      return null;
    }
  }

  /// 检查响应是否包含错误
  /// 常见错误响应: ERROR, +CME ERROR:, etc.
  static bool isErrorResponse(String response) {
    final upperResponse = response.toUpperCase();
    return upperResponse.contains('ERROR') || 
           upperResponse.contains('FAIL') ||
           upperResponse.contains('INVALID');
  }

  /// 检查响应是否为OK
  static bool isOkResponse(String response) {
    return response.toUpperCase().contains('OK');
  }
}
