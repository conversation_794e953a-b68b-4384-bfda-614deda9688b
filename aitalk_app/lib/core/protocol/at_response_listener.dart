import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../bluetooth/bluetooth_manager.dart';

/// AT指令响应监听器
/// 用于监听和捕获设备返回的AT指令响应
class AtResponseListener {
  static StreamSubscription<List<int>>? _subscription;
  static final Map<String, Completer<String>> _pendingRequests = {};
  static final StreamController<String> _responseController = StreamController<String>.broadcast();

  /// 初始化响应监听器
  static Future<void> initialize() async {
    final device = BluetoothManager.currentDevice.value;
    if (device == null) {
      debugPrint('❌ 未连接设备，无法初始化AT响应监听器');
      return;
    }

    try {
      // 查找透传特征值
      final services = await device.discoverServices();
      BluetoothCharacteristic? passthroughCharacteristic;

      for (final service in services) {
        for (final characteristic in service.characteristics) {
          if (characteristic.properties.notify || characteristic.properties.indicate) {
            // 假设这是透传特征值，实际应用中需要根据UUID判断
            passthroughCharacteristic = characteristic;
            break;
          }
        }
        if (passthroughCharacteristic != null) break;
      }

      if (passthroughCharacteristic == null) {
        debugPrint('❌ 未找到透传特征值');
        return;
      }

      // 启用通知
      await passthroughCharacteristic.setNotifyValue(true);

      // 监听数据
      _subscription = passthroughCharacteristic.lastValueStream.listen(
        (data) {
          try {
            final response = utf8.decode(data);
            debugPrint('📥 收到AT响应: $response');
            _responseController.add(response);
            _processResponse(response);
          } catch (e) {
            debugPrint('❌ 解析AT响应失败: $e');
          }
        },
        onError: (error) {
          debugPrint('❌ AT响应监听错误: $error');
        },
      );

      debugPrint('✅ AT响应监听器初始化成功');
    } catch (e) {
      debugPrint('❌ 初始化AT响应监听器失败: $e');
    }
  }

  /// 处理收到的响应
  static void _processResponse(String response) {
    // 检查是否有等待中的请求
    final responseUpper = response.toUpperCase();
    
    // 处理频点响应
    if (responseUpper.contains('+FREQ:')) {
      _completeRequest('FREQ', response);
    }
    
    // 处理速率响应
    if (responseUpper.contains('+RATE:')) {
      _completeRequest('RATE', response);
    }
    
    // 处理错误响应
    if (responseUpper.contains('ERROR') || responseUpper.contains('FAIL')) {
      _completeAllPendingRequests(response);
    }
  }

  /// 完成指定类型的请求
  static void _completeRequest(String type, String response) {
    final completer = _pendingRequests.remove(type);
    if (completer != null && !completer.isCompleted) {
      completer.complete(response);
    }
  }

  /// 完成所有等待中的请求（通常用于错误情况）
  static void _completeAllPendingRequests(String response) {
    for (final completer in _pendingRequests.values) {
      if (!completer.isCompleted) {
        completer.complete(response);
      }
    }
    _pendingRequests.clear();
  }

  /// 等待指定类型的AT响应
  /// [type] 响应类型，如 'FREQ', 'RATE'
  /// [timeout] 超时时间，默认5秒
  static Future<String?> waitForResponse(String type, {Duration timeout = const Duration(seconds: 5)}) async {
    try {
      // 如果已经有相同类型的请求在等待，先取消它
      final existingCompleter = _pendingRequests[type];
      if (existingCompleter != null && !existingCompleter.isCompleted) {
        existingCompleter.complete('CANCELLED');
      }

      // 创建新的等待器
      final completer = Completer<String>();
      _pendingRequests[type] = completer;

      // 等待响应或超时
      final response = await completer.future.timeout(timeout);
      
      // 清理
      _pendingRequests.remove(type);
      
      return response == 'CANCELLED' ? null : response;
    } catch (e) {
      debugPrint('❌ 等待AT响应超时或失败: $e');
      _pendingRequests.remove(type);
      return null;
    }
  }

  /// 获取响应流
  static Stream<String> get responseStream => _responseController.stream;

  /// 清理资源
  static void dispose() {
    _subscription?.cancel();
    _subscription = null;
    _pendingRequests.clear();
    _responseController.close();
  }
}
