import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_demo_ios/core/protocol/at_response_parser.dart';

void main() {
  group('AtResponseParser', () {
    group('parseFrequencyResponse', () {
      test('should parse valid frequency response correctly', () {
        const response = '+FREQ:473200000,473200000,473200000,473200000\r\nOK\r\n';
        final frequency = AtResponseParser.parseFrequencyResponse(response);
        expect(frequency, 473200000);
      });

      test('should parse frequency response with different values', () {
        const response = '+FREQ:483600000,483600000,483600000,483600000\r\nOK\r\n';
        final frequency = AtResponseParser.parseFrequencyResponse(response);
        expect(frequency, 483600000);
      });

      test('should handle multiline response', () {
        const response = 'AT+FREQ?\r\n+FREQ:473200000,473200000,473200000,473200000\r\nOK\r\n';
        final frequency = AtResponseParser.parseFrequencyResponse(response);
        expect(frequency, 473200000);
      });

      test('should return null for invalid response', () {
        const response = 'ERROR\r\n';
        final frequency = AtResponseParser.parseFrequencyResponse(response);
        expect(frequency, isNull);
      });

      test('should return null for malformed frequency response', () {
        const response = '+FREQ:invalid,data\r\nOK\r\n';
        final frequency = AtResponseParser.parseFrequencyResponse(response);
        expect(frequency, isNull);
      });
    });

    group('parseRateResponse', () {
      test('should parse valid rate response correctly', () {
        const response = '+RATE:18,other,params\r\nOK\r\n';
        final rate = AtResponseParser.parseRateResponse(response);
        expect(rate, 18);
      });

      test('should parse rate response with single value', () {
        const response = '+RATE:6\r\nOK\r\n';
        final rate = AtResponseParser.parseRateResponse(response);
        expect(rate, 6);
      });

      test('should handle multiline response', () {
        const response = 'AT+RATE?\r\n+RATE:18,other,params\r\nOK\r\n';
        final rate = AtResponseParser.parseRateResponse(response);
        expect(rate, 18);
      });

      test('should return null for invalid response', () {
        const response = 'ERROR\r\n';
        final rate = AtResponseParser.parseRateResponse(response);
        expect(rate, isNull);
      });

      test('should return null for malformed rate response', () {
        const response = '+RATE:invalid\r\nOK\r\n';
        final rate = AtResponseParser.parseRateResponse(response);
        expect(rate, isNull);
      });
    });

    group('isErrorResponse', () {
      test('should detect ERROR response', () {
        expect(AtResponseParser.isErrorResponse('ERROR\r\n'), isTrue);
        expect(AtResponseParser.isErrorResponse('+CME ERROR: 123\r\n'), isTrue);
        expect(AtResponseParser.isErrorResponse('FAIL\r\n'), isTrue);
        expect(AtResponseParser.isErrorResponse('INVALID COMMAND\r\n'), isTrue);
      });

      test('should not detect error in valid response', () {
        expect(AtResponseParser.isErrorResponse('+FREQ:473200000\r\nOK\r\n'), isFalse);
        expect(AtResponseParser.isErrorResponse('OK\r\n'), isFalse);
      });
    });

    group('isOkResponse', () {
      test('should detect OK response', () {
        expect(AtResponseParser.isOkResponse('OK\r\n'), isTrue);
        expect(AtResponseParser.isOkResponse('+FREQ:473200000\r\nOK\r\n'), isTrue);
      });

      test('should not detect OK in error response', () {
        expect(AtResponseParser.isOkResponse('ERROR\r\n'), isFalse);
        expect(AtResponseParser.isOkResponse('FAIL\r\n'), isFalse);
      });
    });
  });
}
